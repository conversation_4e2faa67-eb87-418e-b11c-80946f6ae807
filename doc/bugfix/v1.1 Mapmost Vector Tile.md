## **动态矢量瓦片（PBF）服务接口 PRD**

| **文档版本** | **V1.0** | **创建日期** | 2025-07-02 |
| **项目名称** | 动态业务数据地图可视化项目 | **模块名称** | 矢量瓦片服务 (Vector Tile Service) |

### 1. 背景与目标

#### 1.1 项目背景
随着业务发展，`event`（事件）数据量持续增长。在前端地图上直接加载大量事件点已导致严重的性能问题，如页面卡顿、加载时间过长等，影响了用户体验和业务操作效率。为解决此问题，我们引入了基于矢量瓦片（Vector Tile）的后端动态渲染技术。

#### 1.2 项目目标
开发一套高性能、可扩展的动态矢量瓦片服务。该服务能够实时从PostGIS数据库中查询业务数据，生成符合Mapbox Vector Tile (MVT)规范的PBF格式数据，以支持前端地图库（Mapmost）实现海量数据点的高效渲染、无级缩放和点聚合效果。

本次迭代将首先实现`event`表的动态瓦片服务，并确保关键参数（如坐标系SRID）的可配置性，以适应未来可能的技术或需求变更。

### 2. 需求范围

*   **实现`event`事件表的动态瓦片服务接口。**
*   接口需支持按地图瓦片坐标（Z/X/Y）进行实时数据查询。
*   接口返回的数据格式必须为`application/vnd.mapbox-vector-tile` (PBF)。
*   PBF数据中需包含关键业务字段，以支持前端的交互式`filter`（过滤）和信息展示。
*   **瓦片服务的目标坐标系（SRID）必须可通过配置文件进行管理**，不得硬编码。
*   **整体架构设计需具备良好的扩展性**，通过独立的Controller和Service实现业务分离，便于未来快速支持其他业务图层。

### 3. 功能需求

| 需求ID | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| F-01 | **创建专用的瓦片服务Controller** | **高** |
| F-02 | **创建`event`事件瓦片API接口** | **高** |
| F-03 | **PBF数据内容定义** | **高** |
| F-04 | **后端硬性数据过滤** | **高** |
| F-05 | **支持前端交互式过滤** | **高** |
| F-06 | **空瓦片处理** | **中** |
| F-07 | **瓦片目标坐标系（SRID）可配置** | **高** |

### 4. 技术实现方案

我们将遵循关注点分离（Separation of Concerns）的设计原则，为矢量瓦片服务创建独立的、专注的业务模块。

#### 4.1 配置管理 (`application.yml`)
在`application.yml`中定义瓦片服务的相关参数，以实现SRID的可配置性。

```yaml
# application.yml
map-services:
  tile:
    # 定义矢量瓦片的目标空间参考标识符 (SRID)。
    # Web地图通常使用 Web Mercator 投影，其SRID为 3857。
    # 我们的源数据 (event.location) 是 WGS 84 坐标系，SRID为 4326。
    target-srid: 3857
```

#### 4.2 数据库层 (Database Layer)
1.  **确认索引：** 必须确保`public.event`表的`location`字段已建立GIST空间索引。
    ```sql
    CREATE INDEX IF NOT EXISTS idx_event_location ON public.event USING GIST (location);
    ```
2.  **Mapper XML实现 (`EventMapper.xml`):**
    SQL查询将接收并使用来自配置文件的`targetSrid`参数，通过`ST_Transform`函数处理坐标系转换。

    ```xml
    <select id="getEventTileAsPbf" resultType="byte[]">
        WITH
        bounds_mercator AS (
            -- 1. 根据z,x,y计算瓦片在目标坐标系(如3857)下的地理边界
            SELECT ST_TileEnvelope(#{z}, #{x}, #{y}) AS geom
        ),
        bounds_wgs84 AS (
            -- 2. 将瓦片边界转换回源数据的坐标系(4326)，以便利用空间索引
            SELECT ST_Transform(geom, 4326) AS geom FROM bounds_mercator
        ),
        mvtgeom AS (
            -- 3. 查询与瓦片边界相交的、未被删除的事件数据
            SELECT
                e.id, e.event_status, e.discovery_time, e.waste_material,
                -- 4. 关键：将源数据坐标(4326)转换为目标瓦片坐标系(如3857)
                ST_AsMVTGeom(
                    ST_Transform(e.location, #{targetSrid}),
                    bounds_mercator.geom,
                    4096, 256, true
                ) AS geom
            FROM
                public.event e, bounds_wgs84
            WHERE
                -- 5. 关键性能优化：在源数据坐标系(4326)下使用空间索引进行快速筛选
                e.location && bounds_wgs84.geom
                AND e.is_deleted = 0
        )
        -- 6. 将所有筛选出的行编码成一个PBF二进制块
        SELECT ST_AsMVT(mvtgeom.*, 'events_layer', 4096, 'geom')
        FROM mvtgeom;
    </select>
    ```

#### 4.3 数据访问层 (Data Access Layer)
1.  **Mapper接口 (`EventMapper.java`):**
    接口方法需能接收`targetSrid`参数。
    ```java
    @Mapper
    public interface EventMapper {
        byte[] getEventTileAsPbf(@Param("z") int z,
                                 @Param("x") int x,
                                 @Param("y") int y,
                                 @Param("targetSrid") int targetSrid);
    }
    ```

#### 4.4 业务逻辑层 (Business Logic Layer)
1.  **服务接口 (`TileService.java`):**
    ```java
    public interface TileService {
        byte[] getEventTile(int z, int x, int y);
    }
    ```
2.  **服务实现 (`TileServiceImpl.java`):**
    实现类将从配置文件中读取`target-srid`的值，并将其传递给Mapper。
    ```java
    @Service
    public class TileServiceImpl implements TileService {
        private final EventMapper eventMapper;

        @Value("${map-services.tile.target-srid}")
        private int targetSrid;

        @Autowired
        public TileServiceImpl(EventMapper eventMapper) {
            this.eventMapper = eventMapper;
        }

        @Override
        public byte[] getEventTile(int z, int x, int y) {
            return eventMapper.getEventTileAsPbf(z, x, y, this.targetSrid);
        }
    }
    ```

#### 4.5 表现层 (Presentation Layer)
创建一个全新的`Controller`来统一管理所有瓦片服务的API端点。

1.  **控制器 (`TileController.java`):**
    ```java
    @RestController
    @RequestMapping("/api/v1/tiles") // 统一的瓦片服务API前缀
    public class TileController {
        private final TileService tileService;

        @Autowired
        public TileController(TileService tileService) {
            this.tileService = tileService;
        }

        @GetMapping(value = "/events/{z}/{x}/{y}.pbf", produces = "application/vnd.mapbox-vector-tile")
        public ResponseEntity<byte[]> getEventTile(@PathVariable int z, @PathVariable int x, @PathVariable int y) {
            byte[] tileData = tileService.getEventTile(z, x, y);

            if (tileData == null || tileData.length == 0) {
                return ResponseEntity.noContent().build();
            }

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/vnd.mapbox-vector-tile");

            return ResponseEntity.ok().headers(headers).body(tileData);
        }

        // 未来可在此处扩展其他图层接口...
    }
    ```

### 5. 接口定义 (API Specification)

#### 5.1 获取事件瓦片数据

*   **功能描述:** 根据指定的瓦片坐标(z,x,y)，获取`event`事件数据的矢量瓦片。
*   **URL:** `/api/v1/tiles/events/{z}/{x}/{y}.pbf`
*   **Method:** `GET`
*   **Path Parameters:**
    *   `z` (integer): 地图缩放级别。
    *   `x` (integer): 瓦片X坐标。
    *   `y` (integer): 瓦片Y坐标。
*   **Success Response:**
    *   **Code:** `200 OK`
    *   **Content-Type:** `application/vnd.mapbox-vector-tile`
    *   **Body:** PBF格式的二进制数据。
    ---
    *   **Code:** `204 No Content`
    *   **Body:** (Empty)
    *   **说明:** 当请求的瓦片范围内不包含任何有效事件数据时返回。
*   **Error Response:**
    *   **Code:** `500 Internal Server Error`
    *   **Body:** (JSON)
    *   **说明:** 数据库查询失败或其他服务器内部错误。

### 6. 前后端协作要点

为确保前端能够正确使用此接口，后端开发完成后，需向前端提供以下关键信息：

1.  **URL模板:** `http://<your_server_address>/api/v1/tiles/events/{z}/{x}/{y}.pbf`
2.  **图层名称 (Source Layer Name):** `'events_layer'` (此名称在Mapper XML中定义)
3.  **可用属性 (Available Properties):** 前端可以在`filter`或`paint`属性中使用的字段名：
    *   `id` (number)
    *   `event_status` (number)
    *   `discovery_time` (timestamp)
    *   `waste_material` (number)