package org.springblade.modules.beachwaste.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springblade.modules.beachwaste.mapper.EventMapper;
import org.springblade.modules.beachwaste.service.ITileService;



/**
 * 瓦片服务实现类
 * 实现动态矢量瓦片生成功能，支持高性能地图数据渲染
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TileServiceImpl implements ITileService {

    private final EventMapper eventMapper;

    /**
     * 从配置文件中读取目标坐标系SRID
     * 默认使用Web Mercator投影(SRID: 3857)
     */
    @Value("${map-services.tile.target-srid:3857}")
    private int targetSrid;

    /**
     * 获取事件数据的矢量瓦片
     * 
     * @param z 地图缩放级别
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @return PBF格式的二进制数据
     */
    @Override
    public byte[] getEventTile(int z, int x, int y) {
        try {
            log.debug("生成事件瓦片: z={}, x={}, y={}, targetSrid={}", z, x, y, targetSrid);
            
            // 调用Mapper层获取瓦片数据
            byte[] tileData = eventMapper.getEventTileAsPbf(z, x, y, targetSrid);
            
            if (tileData != null && tileData.length > 0) {
                log.debug("成功生成事件瓦片，数据大小: {} bytes", tileData.length);
                return tileData;
            } else {
                log.debug("瓦片区域内无事件数据: z={}, x={}, y={}", z, x, y);
                return null;
            }
        } catch (Exception e) {
            log.error("生成事件瓦片失败: z={}, x={}, y={}, error={}", z, x, y, e.getMessage(), e);
            throw new RuntimeException("生成矢量瓦片失败", e);
        }
    }
}