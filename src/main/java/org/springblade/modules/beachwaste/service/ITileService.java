package org.springblade.modules.beachwaste.service;

/**
 * 瓦片服务接口
 * 提供动态矢量瓦片生成功能，支持高性能地图数据渲染
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface ITileService {

    /**
     * 获取事件数据的矢量瓦片
     * 根据指定的瓦片坐标(z,x,y)，生成符合Mapbox Vector Tile规范的PBF格式数据
     * 
     * @param z 地图缩放级别，范围通常为0-18
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @return PBF格式的二进制数据，如果指定区域无数据则返回null或空数组
     */
    byte[] getEventTile(int z, int x, int y);
}