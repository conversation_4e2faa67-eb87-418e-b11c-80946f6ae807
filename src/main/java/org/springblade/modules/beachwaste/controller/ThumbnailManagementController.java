package org.springblade.modules.beachwaste.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.task.ThumbnailCleanupTask;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 缩略图管理控制器
 * 提供缩略图清理的手动触发接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/beachwaste/thumbnail")
@Tag(name = "缩略图管理", description = "缩略图清理管理接口")
public class ThumbnailManagementController {

    private final ThumbnailCleanupTask thumbnailCleanupTask;

    /**
     * 手动触发缩略图清理
     * 用于容量不足时的紧急处理
     *
     * @return 清理结果
     */
    @PostMapping("/cleanup")
    @Operation(summary = "手动触发缩略图清理", description = "立即执行缩略图清理任务，删除所有缩略图文件")
    public R<String> manualCleanup() {
        try {
            log.info("收到手动清理缩略图请求");
            thumbnailCleanupTask.manualCleanup();
            return R.success("缩略图清理任务已成功执行");
        } catch (Exception e) {
            log.error("手动清理缩略图失败", e);
            return R.fail("缩略图清理任务执行失败: " + e.getMessage());
        }
    }
}
