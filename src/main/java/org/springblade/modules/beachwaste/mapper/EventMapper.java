package org.springblade.modules.beachwaste.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.vo.ProcessedStaffStatsVO;

import java.util.List;
import java.util.Map;

/**
 * 事件信息数据访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface EventMapper extends BaseMapper<Event> {

    /**
     * 获取所有事件的年份列表（按年份降序排序）
     *
     * @return 年份列表
     */
	List<Integer> selectEventYears();

	/**
	 * 根据年份获取每月事件数量统计
	 * @param year
	 * @return 统计每月事件数量
	 */
	List<Map<String, Object>> selectMonthlyStatsByYear(@Param("year") Integer year);

	/**
	 * 获取指定年份已处置事件的处理人员统计
	 * @param year 年份
	 * @param eventStatus 事件状态ID（已处置状态）
	 * @return 处理人员及其处理事件数量，从高到低排序
	 */
	List<ProcessedStaffStatsVO> selectProcessedStaffStatsByYear(@Param("year") Integer year, @Param("eventStatus") Long eventStatus);

	/**
     * 获取事件统计信息
     *
     * 此方法用于收集并返回有关事件的统计信息它以键值对的形式返回统计信息，
     * 其中键是统计项的名称，值是对应的统计数值或对象
     *
     * @return 包含事件统计信息的映射表，键为统计项名称，值为对应的统计数值或对象
     */
    Map<String, Object> getEventStats();

    /**
     * 获取指定用户的累计处理事件数量
     * @param userId 用户ID
     * @return 累计处理事件数量
     */
    Integer selectTotalEventsByUserId(@Param("userId") Long userId);

    /**
     * 获取指定用户在当前月的处理事件数量
     * @param userId 用户ID
     * @return 当前月处理事件数量
     */
    Integer selectMonthlyEventsByUserId(@Param("userId") Long userId);

    /**
     * 获取事件数据的矢量瓦片（PBF格式）
     * 根据指定的瓦片坐标(z,x,y)和目标坐标系，生成符合Mapbox Vector Tile规范的PBF数据
     * 
     * @param z 地图缩放级别
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @param targetSrid 目标坐标系SRID（如3857为Web Mercator投影）
     * @return PBF格式的二进制数据，如果无数据则返回null
     */
    @InterceptorIgnore(tenantLine = "true")
    byte[] getEventTileAsPbf(@Param("z") int z,
                             @Param("x") int x,
                             @Param("y") int y,
                             @Param("targetSrid") int targetSrid);

}
