<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.beachwaste.mapper.EventMapper">

    <select id="selectEventYears" resultType="java.lang.Integer">
        SELECT DISTINCT EXTRACT(YEAR FROM discovery_time)::integer as year
        FROM event
        ORDER BY year DESC
    </select>

    <select id="selectMonthlyStatsByYear" resultType="java.util.Map">
        SELECT EXTRACT(MONTH FROM discovery_time)::integer AS month, COUNT(*) AS count
        FROM event
        WHERE EXTRACT(YEAR FROM discovery_time) = #{year}
        GROUP BY EXTRACT(MONTH FROM discovery_time)
    </select>

    <select id="selectProcessedStaffStatsByYear" resultType="org.springblade.modules.beachwaste.pojo.vo.ProcessedStaffStatsVO">
        SELECT u.real_name as staffName, e.handler_staff_id as staffId, COUNT(*) as eventCount
        FROM event e
        JOIN blade_user u ON e.handler_staff_id = u.id
        <where>
            <if test="year != null">
                EXTRACT(YEAR FROM e.discovery_time) = #{year}
            </if>
            <if test="eventStatus != null">
                AND e.event_status = #{eventStatus}
            </if>
            AND e.handler_staff_id IS NOT NULL
        </where>
        GROUP BY e.handler_staff_id, u.real_name
        ORDER BY eventCount DESC
    </select>

    <select id="getEventStats" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_events,
            SUM(CASE WHEN event_status = 2 THEN 1 ELSE 0 END) as processed_events,
            AVG(CASE WHEN handler_report_time IS NOT NULL THEN
                EXTRACT(EPOCH FROM (handler_report_time - discovery_time)) / 3600 ELSE NULL END) as avg_process_time
        FROM event
        WHERE is_deleted = 0
    </select>

    <select id="selectTotalEventsByUserId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM event
        WHERE handler_staff_id = #{userId}
        AND handler_staff_id IS NOT NULL
    </select>

    <select id="selectMonthlyEventsByUserId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM event
        WHERE handler_staff_id = #{userId}
        AND handler_staff_id IS NOT NULL
        AND EXTRACT(YEAR FROM handler_report_time) = EXTRACT(YEAR FROM CURRENT_DATE)
        AND EXTRACT(MONTH FROM handler_report_time) = EXTRACT(MONTH FROM CURRENT_DATE)
    </select>

    <select id="getEventTileAsPbf" resultType="byte[]">
        WITH
            bounds AS (
                -- 1. 将边界框计算合并到一个CTE中，ST_TileEnvelope只计算一次，更高效
                SELECT
                    ST_TileEnvelope(#{z}, #{x}, #{y}) AS mercator_geom, -- 目标坐标系(3857)的边界
                    ST_Transform(ST_TileEnvelope(#{z}, #{x}, #{y}), 4326) AS wgs84_geom -- 源坐标系(4326)的边界
            ),
            mvtgeom AS (
                -- 2. 查询与瓦片边界相交的、未被删除的事件数据
                SELECT
                    e.id, e.event_status, e.discovery_time, e.waste_material,
                    -- 3. 关键：将源数据坐标(4326)转换为目标瓦片坐标系(如3857)
                    ST_AsMVTGeom(
                        ST_Transform(e.location, #{targetSrid}),
                        b.mercator_geom, -- 使用bounds CTE中的mercator边界
                        4096, 256, true
                    ) AS geom
                FROM
                    public.event e, bounds b -- 4. 使用更标准、更简洁的语法引入边界框
                WHERE
                    e.is_deleted = 0
                  -- 5. 关键性能优化：这句的逻辑完全正确，现在语法也更清晰了
                  AND <![CDATA[ e.location && b.wgs84_geom ]]>
            )
        -- 6. 将所有筛选出的行编码成一个PBF二进制块
        SELECT ST_AsMVT(mvtgeom.*, 'events_layer', 4096, 'geom')
        FROM mvtgeom;
    </select>

</mapper>
