package org.springblade.modules.beachwaste.pojo.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 地图事件查询DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "地图事件查询参数")
public class MapEventQueryDTO {

    /**
     * 地图可视范围边界框，格式为 minLng,minLat,maxLng,maxLat
     */
    @Schema(description = "地图边界框，格式：minLng,minLat,maxLng,maxLat", example = "120.7,31.3,120.8,31.4")
    private String bbox;

    /**
     * 事件发现的开始时间，格式 YYYY-MM-DD HH:mm:ss
     */
    @Schema(description = "开始时间")
    private String startDate;

    /**
     * 事件发现的结束时间，格式 YYYY-MM-DD HH:mm:ss
     */
    @Schema(description = "结束时间")
    private String endDate;

    /**
     * 按事件处理状态筛选
     */
    @Schema(description = "事件状态")
    private Long eventStatus;

    /**
     * 按垃圾材质类型筛选
     */
    @Schema(description = "垃圾材质类型")
    private Long wasteMaterial;

}
